const express = require("express");
const cors = require("cors");
const app = express();
const fs = require("fs");
const PORT = 3000;
const apiRoutes = require("./routes");
app.use(express.json({ limit: "1024mb" }));
app.use(express.urlencoded({ limit: "1024mb", extended: false }));

const allowedOrigins = [
  "https://wapal.io",
  "https://launchpad.wapal.io",
  "https://staging-mainnet.wapal.io",
  "https://staging.theloonies.xyz",
  "https://theloonies.xyz",
  "http://localhost:3000",
  "https://aggregator.wapal.io",
  "https://staging-marketplace.wapal.io",
  "https://staging-mainnet.theloonies.xyz",
];

const corsOptions = {
  origin: function (origin, callback) {
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error("Not allowed by CORS"));
    }
  },
  methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
  credentials: true,
  optionsSuccessStatus: 204,
};

app.use(cors(corsOptions));

app.use("/api", apiRoutes);
app.get("/", async (req, res) => {
  try {
    res.send("Helooooo");
  } catch (error) {
    res.send("errorrrrrrrr");
  }
});
app.listen(PORT, () => console.info(`Listening on ${PORT}`));
