const redis = require("redis");
const { promisify } = require("util");
const redisURL = "redis://redis:6379";
const client = redis.createClient();
client.connect();
// if (typeof client.hGet !== "function" || typeof client.hSet !== "function") {
//   console.error(
//     "Error: The client object does not have expected methods (hget, hset)."
//   );
//   client.quit();
//   process.exit(1);
// }
const hgetAsync = promisify(client.hGet).bind(client);
const hsetAsync = promisify(client.hSet).bind(client);

exports.storeLinkToCache = async (linkset) => {
  try {
    const { id, link } = linkset;
    await hsetAsync("image_links_to_ids", link, id);
  } catch (error) {
    console.error("Error storing links in Redis cache:", error);
  }
};
// async function get(key, fetcher) {
//   // bypass cache if not connected to redis.
//   if (!client.cache.isReady) {
//     return await fetcher();
//   }

//   return new Promise(async (resolve, reject) => {
//     const value = await this.cache.GET(key);
//     if (value) {
//       // return value if found in cache
//       return resolve(JSON.parse(value));
//     }
//     // if value is not in cache, fetch and return it
//     const result = await fetcher();
//     await this.cache.set(key, JSON.stringify(result), { EX: this.ttl });
//     return resolve(result);
//   });
// }

exports.getIdByLinkFromCache = async (link) => {
  try {
    console.log("getting image from cache,");
    const id = await client.hGet("image_links_to_ids", link);
    console.log("id from cache>", id);
    return id;
  } catch (error) {
    console.error("Error retrieving ID from Redis cache:", error);
    return null;
  }
};

// const imageLinks = [
//   { id: "1", link: "https://example.com/image1.jpg" },
//   { id: "2", link: "https://example.com/image2.jpg" },
// ];

// this.storeLinkToCache(
//   (item = { id: "1", link: "https://example.com/image1.jpg" })
// );
// this.storeLinkToCache(
//   (item = { id: "2", link: "https://example.com/image2.jpg" })
// );

// const linkToRetrieve = "https://example.com/image1.jpg";
// this.getIdByLinkFromCache(linkToRetrieve)
//   .then((id) => {
//     if (id) {
//       console.log(`ID for link ${linkToRetrieve}: ${id}`);
//     } else {
//       console.log(`No ID found for link ${linkToRetrieve}`);
//     }
//     // client.quit();
//   })
//   .catch((error) => console.error("Error:", error));
