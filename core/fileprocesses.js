const fs = require("fs");
const path = require("path");
const downloadImage = require("./download");
const { getIdByLinkFromCache } = require("./cache/main");
const { COMPRESSED_FILES_FOLDER } = require("../constants");
const dns = require("dns").promises;
const { URL } = require("url");
const net = require("net");

const isLocalOrPrivateIp = async (url) => {
  try {
    const hostname = new URL(url).hostname;

    if (
      hostname === "localhost" ||
      hostname === "127.0.0.1" ||
      hostname === "::1"
    )
      return true;

    const addresses = await dns.lookup(hostname, { all: true });

    return addresses.some((addr) => {
      const ip = addr.address;
      const parts = ip.split(".");
      return (
        ip.startsWith("10.") ||
        ip.startsWith("192.168.") ||
        (parts[0] === "172" && parts[1] >= 16 && parts[1] <= 31) ||
        ip === "127.0.0.1" ||
        ip === "::1"
      );
    });
  } catch (err) {
    console.error("IP check failed:", err);
    return true;
  }
};

exports.sendFileToRes = async (req, res) => {
  try {
    let { l, rs } = req.query;
    const validImageExtensions = /\.(jpe?g|png|gif|webp|bmp|tiff?)$/i;
    if (!l || !validImageExtensions.test(l)) {
      return res
        .status(400)
        .json({ msg: "Invalid or no media link provided." });
    }

    if (await isLocalOrPrivateIp(l)) {
      return res
        .status(403)
        .json({ msg: "Forbidden: Local or private IP access not allowed." });
    }

    const link = l;
    rs = Number(rs) || 512;

    const id = await getIdByLinkFromCache(`${l}${rs}`);
    if (!id) {
      downloadImage(link, rs);
      return res.send(link);
    }

    const required_file = `${COMPRESSED_FILES_FOLDER}/${id}`;
    const exists = fs.existsSync(required_file);

    if (!exists) {
      return res.send(link);
    }

    const mimeTypes = {
      jpg: "image/jpeg",
      jpeg: "image/jpeg",
      png: "image/png",
      gif: "image/gif",
      webp: "image/webp",
      bmp: "image/bmp",
      tiff: "image/tiff",
      tif: "image/tiff",
    };

    const ext = path.extname(required_file).slice(1).toLowerCase();
    const contentType = mimeTypes[ext] || "application/octet-stream";

    const options = {
      root: "./",
      dotfiles: "deny",
      headers: {
        "Content-Type": contentType,
        "x-timestamp": Date.now(),
        "x-sent": true,
        "Cache-Control": "public,max-age=86400, immutable",
        Expires: new Date(Date.now() + 86400000).toUTCString(),
      },
    };

    res.sendFile(required_file, options, function (err) {
      if (err) {
        console.log(err);
        return res.send(link);
      }
    });
  } catch (error) {
    console.log(error);
    res.status(500).send("Internal Server Error");
  }
};
