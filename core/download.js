const { COMPRESSED_FILES_FOLDER } = require("../constants");
const { storeLinkToCache } = require("./cache/main");
const { size_to_quality } = require("./utils/size_to_quality");

const axios = require("axios");
const fs = require("fs");
const path = require("path");
const sharp = require("sharp");
const imagemin = require("imagemin");
const imageminGifsicle = require("imagemin-gifsicle");

const { execFile } = require("node:child_process");

const randomString = (length) => {
  var result = "";
  var characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxy1234567890_";
  var charactersLength = characters.length;
  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};
async function downloadImage(mediaUrl, rs) {
  const folderPath = COMPRESSED_FILES_FOLDER;

  if (!fs.existsSync(folderPath)) {
    fs.mkdirSync(folderPath, { recursive: true });
  }

  // let ext = path.extname(mediaUrl) || ".png";
  let ext = ".webp";
  const imageId = randomString(12);
  const filePath = path.join(folderPath, `${imageId}${ext}`);
  axios({
    method: "get",
    url: mediaUrl,
    responseType: "arraybuffer",
  })
    .then(async (response) => {
      const mediaBuffer = Buffer.from(response.data);
      const image = sharp(mediaBuffer);
      const mediaType = path.extname(mediaUrl).toLowerCase();
      const imageSize = mediaBuffer.length;
      const quality = size_to_quality(imageSize);
      if (
        mediaType === ".jpg" ||
        mediaType === ".jpeg" ||
        mediaType === ".png"
      ) {
        await image.resize(rs, rs).webp({ quality }).toFile(`${filePath}`);
      } else if (mediaType === (".gif" || ".GIF")) {
        // execFile(gifsicle, ["-o", filePath, mediaBuffer], (error) => {
        //   console.log(error);
        // });

        const compressedBuffer = await imagemin.buffer(mediaBuffer, {
          plugins: [
            imageminGifsicle({
              optimizationLevel: 3, // Adjust optimization level as needed (0 to 3)
            }),
          ],
        });

        fs.writeFileSync(filePath, compressedBuffer);
      } else {
        // GIF or Video: Save without compression
        fs.writeFileSync(filePath, mediaBuffer);
      }
      await storeLinkToCache({
        id: `${imageId}${ext}`,
        link: `${mediaUrl}${rs}`,
      });

      console.log(`Media downloaded and saved to: ${filePath}`);
    })
    .catch((error) => {
      // console.log(error);
      console.error("Error downloading the media:", error.message);
    });
}

module.exports = downloadImage;
